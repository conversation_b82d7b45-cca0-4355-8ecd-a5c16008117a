import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

sns.set_style("whitegrid")

df = pd.read_csv('hypertension_dataset.csv')

df.head()

df.info()

df.describe().T

df.columns.to_list()

df.dtypes

categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
print(categorical_cols)

numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
print(numerical_cols)

df.isnull().sum()

df['Medication'].fillna('None', inplace=True)











































