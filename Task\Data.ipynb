import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

sns.set_style("whitegrid")

df = pd.read_csv('Data.csv')

df.head()

df.info()

df.describe().T

df.columns.to_list()

df.dtypes

categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
print(categorical_cols)

numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
print(numerical_cols)

df.isnull().sum()

for col in numerical_cols :
    df[col].fillna(df[col].mean(), inplace=True)
for col in categorical_cols:
    df[col] = df[col].fillna(df[col].mode()[0])

df.drop_duplicates(inplace=True)

cat_fields = ['person_gender', 'person_education', 'person_home_ownership', 'loan_intent', 'previous_loan_defaults_on_file']
for col in cat_fields:
    df[col] = df[col].astype('category')
    
num_fields = ['person_age', 'person_income', 'loan_amnt', 'loan_int_rate', 'credit_score']
for col in num_fields:
    df[col] = pd.to_numeric(df[col], errors='coerce')

for col in ['person_income', 'loan_amnt', 'loan_int_rate', 'credit_score']:
    Q1 = df[col].quantile(0.25)
    Q3 = df[col].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    df = df[(df[col] >= lower_bound) & (df[col] <= upper_bound)]

df['previous_loan_defaults_on_file'] = df['previous_loan_defaults_on_file'].map({'Yes':1, 'No':0})

df['loan_status'] = df['loan_status'].map({'Approved':1, 'Rejected':0}) # لو نصوص

df['dti_ratio'] = df['loan_amnt'] / (df['person_income'] + 1)
bins = [0,2,5,50]
labels = ["0-2", "3-5", "5+"]
df['emp_exp_bin'] = pd.cut(df['person_emp_exp'], bins=bins, labels=labels, right=False)

sns.histplot(df['person_age'])
plt.show()

sns.boxenplot(df['person_income'])
plt.show()

sns.countplot(x = 'person_gender', data = df)
plt.show()

plt.figure(figsize=(10, 6))
sns.heatmap(df[numerical_cols].corr(), annot=True, cmap='coolwarm')
plt.show()

